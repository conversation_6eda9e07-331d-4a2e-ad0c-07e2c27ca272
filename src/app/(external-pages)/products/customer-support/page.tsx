"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Customer Support with AI - Telex",
    description: "Transform customer support with intelligent AI-powered solutions. Provide instant, accurate responses and deliver exceptional customer experiences around the clock.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import Guides from "../components/single-product/Guides";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/app-performance-bg.svg";
import CaseOne from "../_assets/case-image-one.svg";
import CaseTwo from "../_assets/case-image-two.svg";
import { technologies } from "../data/technologies";

const firstData = [
  {
    id: 1,
    title: "AI-Powered Customer Support",
    content: `Leverage advanced AI to provide instant, accurate responses to customer inquiries. Enhance support efficiency with intelligent automation.`,
  },
  {
    id: 2,
    title: "24/7 Support Availability",
    content: `Ensure round-the-clock customer support with AI chatbots that never sleep. Provide immediate assistance whenever your customers need it.`,
  },
  {
    id: 3,
    title: "Smart Ticket Management",
    content: `Automatically categorize and prioritize support tickets using AI. Route inquiries to the right team members for faster resolution times.`,
  },
  {
    id: 4,
    title: "Personalized Customer Experience",
    content: `Deliver tailored support experiences with AI that learns from past interactions and understands customer preferences.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex AI reduced response time by 80%",
    image: CaseOne?.src,
    content: `A leading e-commerce platform transformed their customer support
            with Telex AI, dramatically improving response times and customer satisfaction.`,
  },
  {
    id: 2,
    title: "AI-driven support automation success",
    image: CaseTwo.src,
    content: `See how our AI support system helped a SaaS company handle 3x more tickets while maintaining quality and reducing costs.`,
  },
  {
    id: 3,
    title: "Transforming customer experience with AI",
    image: CaseOne.src,
    content: `Learn how a fintech startup used Telex AI to provide 24/7 personalized support and increased customer satisfaction by 45%.`,
  },
  {
    id: 4,
    title: "Scaling support operations with AI",
    image: CaseTwo?.src,
    content: `Discover how a growing startup leveraged Telex AI to scale their
            support operations without increasing headcount.`,
  },
];

const CustomerSupport = () => {
  return (
    <>
      <Hero
        breadCumbs="AI-Powered Customer Support"
        title="Intelligent {{Customer Support}} with AI"
        content="Transform your customer support with AI-powered automation. Provide instant, accurate responses and deliver exceptional customer experiences around the clock."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="AI-Powered Customer Support Solutions designed for modern businesses."
        items={firstData}
      />
      <CaseStudy
        tag="Success Stories"
        subheading={`Companies using Telex AI-powered customer support have seen dramatic improvements in response times, customer satisfaction, and operational efficiency. See how our intelligent support solutions can transform your customer service.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Seamless support across all communication channels"
        items={technologies}
      />
      <Guides />
      <Faq />
      <KeepTrack
        title="Elevate your customer support with Telex AI-powered solutions"
        content="Transform your customer support operations with intelligent automation, real-time insights, and personalized experiences. Try Telex today and see the difference AI can make in your customer service."
      />
    </>
  );
};

export default CustomerSupport;
