"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";


export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Cloud Monitoring - Telex",
    description: "Monitor your cloud infrastructure with comprehensive tracking. Ensure optimal cloud performance, manage resources efficiently, and maintain system reliability.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  }
};
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import Guides from "../components/single-product/Guides";
import KeepTrack from "../components/single-product/KeepTrack";
import ImageBg from "../_assets/cloud-monitoring-bg.svg";
import CaseOne from "../_assets/cloud-case-one.svg";
import CaseTwo from "../_assets/cloud-case-two.svg";
import { cloudtechnologies } from "../data/cloud-technologies";
import { cloudMonitoringFaq } from "../data/cloud-monitoring-faqs";

const firstData = [
  {
    id: 1,
    title: "Multi-cloud Environment Monitoring",
    content: `Monitor workloads across different cloud providers in one platform.`,
  },
  {
    id: 2,
    title: "Cost Management Monitoring",
    content: `Keep track of resource usage to avoid over-provisioning and unnecessary costs in your cloud infrastructure.`,
  },
  {
    id: 3,
    title: "Service Health Checks Monitoring",
    content: `Ensure all cloud services, including databases and serverless functions, are operating efficiently.`,
  },
  {
    id: 4,
    title: "Comprehensive Resource Monitoring",
    content: `Track resource utilization like storage, compute power, and networking to avoid over-provisioning or under-utilization.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "AWS EC2 Instance Monitoring",
    image: CaseOne?.src,
    content: `Track the performance and uptime of your EC2 instances.`,
  },
  {
    id: 2,
    title: "Kubernetes Monitoring",
    image: CaseTwo.src,
    content: `Monitor Kubernetes clusters across multi-cloud environments to ensure smooth container orchestration.`,
  },
  {
    id: 3,
    title: "Cloud Storage Tracking",
    image: CaseOne?.src,
    content: `Get insights into cloud storage usage to optimize costs and resources.`,
  },
  {
    id: 4,
    title: "Kubernetes Monitoring",
    image: CaseTwo.src,
    content: `Monitor Kubernetes clusters across multi-cloud environments to ensure smooth container orchestration.`,
  },
];

const Page = () => {
  return (
    <>
      <Hero
        breadCumbs="Cloud Monitoring"
        title="Master Your {{Cloud Infrastructure}} with Intelligent {{Monitoring}}"
        content="Get full visibility into your cloud infrastructure across multiple platforms. Monitor resource usage, scale efficiently, and detect issues before they impact performance, all while ensuring cost-effectiveness in managing your cloud environment."
        routeName="Get Started"
        learnMoreName="Learn more"
        routeLink="#"
        learnMoreLink="#"
      />
      <ImageSection image={ImageBg} />
      <ApplicationTools
        heading="Cloud Performance Monitoring Tool designed for you."
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`Teams today rely on over 12 monitoring tools on average to gather and analyze the vast amounts of data generated across modern hybrid and server environments. Managing such a wide range of tools can strain your entire organization.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Monitor your cloud environemnts seamlessly, regardless of the technology."
        items={cloudtechnologies}
      />
      <Guides />
      <Faq faq={cloudMonitoringFaq} />
      <KeepTrack
        title="Optimize Your Cloud Infrastructure Today"
        content="Start monitoring your cloud infrastructure with Telex and optimize performance and costs. Get a free trial now."
      />
    </>
  );
};

export default Page;
