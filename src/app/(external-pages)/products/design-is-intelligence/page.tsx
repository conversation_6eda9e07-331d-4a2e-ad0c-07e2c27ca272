import { ArrowR<PERSON> } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { Metadata } from "next";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Design is Intelligence - Telex",
    description: "Discover how intelligent design transforms user experiences. Explore the intersection of design and intelligence in modern digital solutions.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import { Button } from "~/components/ui/button";
import {
  cloudStorageIcon,
  contactUsBg,
  customerSupportIcon,
  discordLogo,
  dockerLogo,
  dynatraceLogo,
  fastDeliveryIcon,
  girlAvatar,
  glitchLogo,
  googleLogo,
  imageAiAnalysisIcon,
  imageProcessingIcon,
  mobileModernWayToBuild,
  netflixLogo,
  romeLogo,
  terserLogo,
  vercelLogo,
  youtubeLogo,
} from "../_assets";

function Page() {
  return (
    <main>
      <header className="bg-white py-9 md:py-20 px-6 md:px-8">
        <div className="max-w-[1056px] px-16 mx-auto flex justify-between items-center">
          <div className="max-w-[410px]">
            <h1 className="text-5xl leading-[67.2px] font-bold mb-3">
              Design is intelligence made visible
            </h1>
            <p className="text-gray-900/70 mb-6 max-w-[85%]">
              Build alongside half a million developers and businesses like you.
            </p>
            <div className="flex gap-4">
              <Link href="/auth/sign-up">
                <Button className="bg-gradient-to-b from-[#8860F8] to-[#7141F8] hover:bg-opacity-80 cursor-pointer text-white font-bold hover:opacity-90">
                  Sign up
                </Button>
              </Link>
              <Link href="/auth/sign-up">
                <Button
                  variant="outline"
                  className="bg-transparent border-white text-white hover:bg-white/20"
                >
                  Learn more
                </Button>
              </Link>
            </div>
          </div>
          <div className="w-full">
            <Image
              src={mobileModernWayToBuild}
              width={501}
              height={635}
              alt=""
            />
          </div>
        </div>
      </header>
      <section className="py-9 space-y-6 max-w-[1056px] mx-auto px-6 md:px-8 md:py-28">
        <div className="grid grid-cols-3 gap-6">
          <div>
            <Image
              src={cloudStorageIcon}
              width={32}
              height={32}
              alt=""
              className="mb-6"
            />
            <p className="mb-4 font-bold">Cloud Storage</p>
            <p className="text-sm leading-[25.2px] text-gray-900/55 mb-6">
              Leverages globally distributed edge caches to accelerate content
              delivery lipsum
            </p>
            <Link
              href={"#"}
              className="text-sm text-primary-500 font-bold flex items-center gap-[6px] hover:text-primary-500/90"
            >
              Learn more <ArrowRight size={14} />
            </Link>
          </div>
          <div>
            <Image
              src={imageAiAnalysisIcon}
              width={32}
              height={32}
              alt=""
              className="mb-6"
            />
            <p className="mb-4 font-bold">Image AI analysis</p>
            <p className="text-sm leading-[25.2px] text-gray-900/55 mb-6">
              Leverages globally distributed edge caches to accelerate content
              delivery lipsum
            </p>
            <Link
              href={"#"}
              className="text-sm text-primary-500 font-bold flex items-center gap-[6px] hover:text-primary-500/90"
            >
              Learn more <ArrowRight size={14} />
            </Link>
          </div>
          <div>
            <Image
              src={imageProcessingIcon}
              width={32}
              height={32}
              alt=""
              className="mb-6"
            />
            <p className="mb-4 font-bold">Image processing</p>
            <p className="text-sm leading-[25.2px] text-gray-900/55 mb-6">
              Leverages globally distributed edge caches to accelerate content
              delivery lipsum
            </p>
            <Link
              href={"#"}
              className="text-sm text-primary-500 font-bold flex items-center gap-[6px] hover:text-primary-500/90"
            >
              Learn more <ArrowRight size={14} />
            </Link>
          </div>
        </div>
      </section>
      <section className="bg-gray-50 py-9 space-y-6 px-6 md:px-8 md:py-28">
        <div className="flex flex-col items-center mx-auto gap-12 max-w-[1056px]">
          <div className="flex gap-20 items-center justify-center">
            <Image src={netflixLogo} width={101} height={27} alt="" />
            <Image src={googleLogo} width={101} height={27} alt="" />
            <Image src={vercelLogo} width={101} height={27} alt="" />
            <Image src={youtubeLogo} width={101} height={27} alt="" />
            <Image src={dockerLogo} width={101} height={27} alt="" />
          </div>
          <div className="max-w-[744px] mx-auto">
            <p className="text-gray-900 text-center leading-[28.7px]">
              We’ve done it carefully and simply. Combined with the ingredients
              make for beautiful landings. It is definitely the tool you need to
              speed up your design process.
            </p>
          </div>
          <Link href="/auth/sign-up">
            <Button className="bg-gradient-to-b from-[#8860F8] to-[#7141F8] hover:bg-opacity-80 cursor-pointer text-white font-bold hover:opacity-90">
              Sign up
            </Button>
          </Link>
        </div>
      </section>
      <section className="py-9 space-y-6 max-w-[1056px] mx-auto px-6 md:px-8 md:py-28">
        <div className="flex gap-20 items-center">
          <div className="max-w-[456px]">
            <h3 className="font-bold text-3xl leading-[44.8px]">
              Ready to get started?
            </h3>
            <p className="text-gray-900/55 leading-[28.8px]">
              Explore products, or create an account instantly and start
              accepting payments. You can also contact us to design a custom
              package for your business.
            </p>
            <div className="mt-6 flex gap-3">
              <Link href={"/auth/sign-up"}>
                <Button className="bg-gradient-to-b from-[#8860F8] to-[#7141F8] hover:bg-opacity-80 cursor-pointer text-white font-bold gap-2">
                  Start now <ArrowRight size={15} />
                </Button>
              </Link>
              <Link href={"#"}>
                <Button className="bg-none hover:bg-opacity-80 cursor-pointer  font-bold gap-2">
                  Contact sales <ArrowRight size={15} />
                </Button>
              </Link>
            </div>
          </div>
          <div className="flex gap-6">
            <div>
              <Image
                src={fastDeliveryIcon}
                width={32}
                height={32}
                alt=""
                className="mb-6"
              />
              <p className="mb-4 font-bold">Fast delivery</p>
              <p className="text-sm leading-[25.2px] text-gray-900/55 mb-6">
                Amet minim mollit non deserunt ullamco est sit aliqua dolor do
                amet sint. Velit officia consequat.
              </p>
              <Link
                href={"#"}
                className="text-sm text-primary-500 font-bold flex items-center gap-[6px] hover:text-primary-500/90"
              >
                Pricing details <ArrowRight size={14} />
              </Link>
            </div>
            <div>
              <Image
                src={customerSupportIcon}
                width={32}
                height={32}
                alt=""
                className="mb-6"
              />
              <p className="mb-4 font-bold">Customer support</p>
              <p className="text-sm leading-[25.2px] text-gray-900/55 mb-6">
                Amet minim mollit non deserunt ullamco est sit aliqua dolor do
                amet sint. Velit officia consequat.
              </p>
              <Link
                href={"#"}
                className="text-sm text-primary-500 font-bold flex items-center gap-[6px] hover:text-primary-500/90"
              >
                Go there <ArrowRight size={14} />
              </Link>
            </div>
          </div>
        </div>
      </section>
      <section className="py-9 space-y-6 px-6 md:px-8 md:py-28 bg-gray-50">
        <div className="flex flex-row-reverse justify-end max-w-[1056px] mx-auto gap-20 items-center">
          <div className="max-w-[456px]">
            <h4 className="text-sm text-primary-500 font-medium uppercase">
              Award winning support
            </h4>
            <h1 className="text-[40px] leading-[67.2px] font-bold mb-3">
              We’re here to help
            </h1>
            <p className="text-gray-900/55 mb-5 max-w-[85%] leading-[32.4px]">
              Amet minim mollit non deserunt ullamco est sit aliqua dolor do
              amet sint velit officia consequat duis enim velit mollit.
            </p>
            <div>
              <Link href="/auth/sign-up">
                <Button className="bg-gradient-to-b from-[#8860F8] to-[#7141F8] gap-1 hover:bg-opacity-80 cursor-pointer text-white font-bold hover:opacity-90">
                  Get started
                  <ArrowRight size={14} />
                </Button>
              </Link>
            </div>
          </div>
          <div className="w-full">
            <Image
              className="w-full"
              src={"/images/we-are-here-to-help.jpg"}
              width={800}
              height={500}
              alt=""
            />
          </div>
        </div>
      </section>
      <section className="py-9 space-y-6 px-6 md:px-8 md:py-28">
        <div className="flex flex-col items-center mx-auto gap-12 max-w-[1056px]">
          <div className="flex gap-20 items-center justify-center pb-[29px] border-b border-gray-900/20">
            <Image src={romeLogo} width={101} height={27} alt="" />
            <Image src={discordLogo} width={101} height={27} alt="" />
            <Image src={dynatraceLogo} width={101} height={27} alt="" />
            <Image src={terserLogo} width={101} height={27} alt="" />
            <Image src={glitchLogo} width={101} height={27} alt="" />
            <Image src={netflixLogo} width={101} height={27} alt="" />
          </div>
          <div className="max-w-[744px] mx-auto">
            <p className="text-gray-900 text-center leading-[28.7px]">
              We’ve done it carefully and simply. Combined with the ingredients
              make for beautiful landings. It is definitely the tool you need to
              speed up your design process.
            </p>
            <div className="flex flex-col items-center justify-center gap-3 mt-10">
              <div
                className="size-12 bg-cover bg-center rounded-full"
                style={{ backgroundImage: `url(${girlAvatar.src})` }}
              ></div>
              <div>
                <p className="text-gray-900 text-sm">Kristin Watson </p>
                <p className="text-primary-500">/ CEO, Rome</p>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="relative py-9 space-y-6 mx-auto md:pt-28 md:pb-[400px]">
        <div
          className="w-full h-[256px]"
          style={{ backgroundImage: `url(${contactUsBg.src})` }}
        ></div>
        <div
          className="absolute bottom-52 translate-x-[25%] max-w-[936px] h-[320px] bg-white flex items-center rounded-2xl"
          style={{ boxShadow: "0px 20px 50px 0px rgba(18, 17, 39, 0.08)" }}
        >
          <div className="px-12 flex justify-between items-center">
            <div className="max-w-[468px]">
              <h4 className="text-sm text-primary-500 font-medium uppercase">
                Contact Us
              </h4>
              <h1 className="text-[40px] leading-[67.2px] font-bold mb-3">
                You have a new project? or want to say hello...
              </h1>
              <p className="font-bold"><EMAIL></p>
            </div>
            <div className="max-w-[20%]">
              <p className="font-bold text-gray-900 mb-4">Offices</p>
              <p className="text-gray-900/55 text-sm mb-4">
                1901 Thornridge Cir. Shiloh, Hawaii 81063
              </p>
              <p className="font-bold text-gray-900/55 text-sm">
                8502 Preston Rd. Inglewood, Maine 98380
              </p>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}

export default Page;
